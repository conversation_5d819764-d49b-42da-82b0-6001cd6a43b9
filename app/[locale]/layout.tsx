import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "../globals.css";
import Footer from "../components/Footer";
import Header from "../components/Header";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "LightQuant - AI-Powered Crypto Trading Bots",
  description:
    "Discover intelligent crypto trading bots trained on real market data. Optimize profits, reduce risk, and automate your strategy with LightQuant's AI-driven platform.",
  icons: {
    icon: "/favicon.png",
  },
};

const locales = ['en', 'vi'];

export default async function RootLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) notFound();

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body className={inter.className}>
        <NextIntlClientProvider messages={messages}>
          <div className="min-h-screen">
            <Header />
            <main>{children}</main>
            <Footer />
          </div>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
