"use client";

import { Mail, MessageCircle, Send, Twitter } from "lucide-react";
import { useTranslations } from "next-intl";
import LanguageSwitcher from "./LanguageSwitcher";

const Footer = () => {
  const _t = useTranslations("footer");
  return (
    <footer className="bg-gray-900 text-white py-16 border-t border-gray-800">
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid md:grid-cols-4 gap-8 mb-12">
          {/* Brand */}
          <div className="md:col-span-1">
            <h3 className="font-heading text-2xl font-bold mb-4">
              Light<span className="text-blue-400">Quant</span>
            </h3>
            <p className="font-body text-gray-400 leading-relaxed">{_t("brand_description")}</p>
          </div>

          {/* Links */}
          <div className="md:col-span-2 grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="font-heading font-semibold mb-4 text-blue-400">{_t("platform")}</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="/about" className="font-body hover:text-white transition-colors">
                    {_t("about_us")}
                  </a>
                </li>
                <li>
                  <a href="/blog" className="font-body hover:text-white transition-colors">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="/chart" className="font-body hover:text-white transition-colors">
                    Chart
                  </a>
                </li>
                <li>
                  <a href="/marketplace" className="font-body hover:text-white transition-colors">
                    Bot Marketplace
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-heading font-semibold mb-4 text-blue-400">{_t("legal")}</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="/terms" className="font-body hover:text-white transition-colors">
                    {_t("terms_of_service")}
                  </a>
                </li>
                <li>
                  <a href="/privacy" className="font-body hover:text-white transition-colors">
                    {_t("privacy_policy")}
                  </a>
                </li>
                <li>
                  <a href="/risk" className="font-body hover:text-white transition-colors">
                    {_t("risk_disclosure")}
                  </a>
                </li>
                <li>
                  <a href="/api" className="font-body hover:text-white transition-colors">
                    {_t("api_documentation")}
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Contact & Social */}
          <div className="md:col-span-1">
            <h4 className="font-heading font-semibold mb-4 text-blue-400">{_t("connect")}</h4>
            <div className="space-y-4">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center gap-2 font-body text-gray-400 hover:text-white transition-colors"
              >
                <Mail className="w-4 h-4" />
                {_t("support_email")}
              </a>

              <div className="flex gap-4">
                <a
                  href="#"
                  className="p-2 bg-gray-800 rounded-lg hover:bg-blue-600 transition-colors"
                >
                  <Twitter className="w-5 h-5" />
                </a>
                <a
                  href="#"
                  className="p-2 bg-gray-800 rounded-lg hover:bg-purple-600 transition-colors"
                >
                  <MessageCircle className="w-5 h-5" />
                </a>
                <a
                  href="#"
                  className="p-2 bg-gray-800 rounded-lg hover:bg-blue-500 transition-colors"
                >
                  <Send className="w-5 h-5" />
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="flex flex-col md:flex-row items-center gap-4">
            <p className="font-body text-gray-400 text-sm">{_t("copyright")}</p>
            <LanguageSwitcher />
          </div>
          <div className="flex gap-4 font-body text-gray-400 text-sm">
            <span>{_t("social_twitter")}</span>
            <span>{_t("social_discord")}</span>
            <span>{_t("social_telegram")}</span>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
